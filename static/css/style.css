/* Custom styles for Email Scraper */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: 600;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

.status-card {
    text-align: center;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.status-card.pending {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.status-card.running {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.status-card.completed {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.status-card.failed {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.status-number {
    font-size: 2rem;
    font-weight: bold;
    display: block;
}

.status-label {
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge {
    font-size: 0.75rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-sm {
    font-size: 0.8rem;
}

.form-control, .form-select {
    border-radius: 0.375rem;
}

.alert {
    border-radius: 0.5rem;
    border: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .status-card {
        margin-bottom: 0.5rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Animation for status updates */
.status-card {
    transition: all 0.3s ease;
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* Custom file input styling */
.form-control[type="file"] {
    padding: 0.375rem 0.75rem;
}

/* Progress bars */
.progress {
    height: 0.5rem;
    border-radius: 0.25rem;
}

/* Custom scrollbar for tables */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Footer styling */
footer {
    margin-top: auto;
}

/* Card hover effects */
.card:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.15s ease-in-out;
}

/* Button loading state */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status badge colors */
.badge.bg-pending {
    background-color: #ffc107 !important;
}

.badge.bg-running {
    background-color: #17a2b8 !important;
}

.badge.bg-completed {
    background-color: #28a745 !important;
}

.badge.bg-failed {
    background-color: #dc3545 !important;
}

/* Pagination styles */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding: 0.75rem 0;
    border-top: 1px solid #dee2e6;
}

.pagination-info {
    color: #6c757d;
    font-size: 0.875rem;
    margin: 0;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.pagination-per-page {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6c757d;
}

.pagination .page-link {
    color: #0d6efd;
    border-color: #dee2e6;
    padding: 0.375rem 0.75rem;
}

.pagination .page-link:hover {
    color: #0a58ca;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: #fff;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

.pagination-sm .page-link {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Responsive pagination */
@media (max-width: 768px) {
    .pagination-container {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }

    .pagination-controls {
        justify-content: center;
        flex-wrap: wrap;
    }

    .pagination-info {
        text-align: center;
    }

    .pagination .page-item {
        display: none;
    }

    .pagination .page-item:first-child,
    .pagination .page-item:last-child,
    .pagination .page-item.active,
    .pagination .page-item.active + .page-item,
    .pagination .page-item.active - .page-item {
        display: block;
    }
}

/* Table with pagination */
.table-with-pagination {
    margin-bottom: 0;
}

.table-with-pagination .table-responsive {
    margin-bottom: 0;
}

/* Loading state for pagination */
.pagination-loading {
    opacity: 0.6;
    pointer-events: none;
}
