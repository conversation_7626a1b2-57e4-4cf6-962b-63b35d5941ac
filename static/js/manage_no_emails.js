$(document).ready(function() {
    let noEmailsCompanies = [];
    let reviewCompanies = [];
    let filteredNoEmailsCompanies = [];
    let filteredReviewCompanies = [];

    // Load data on page load
    loadNoEmailsCompanies();
    loadReviewCompanies();

    // Event listeners for No Emails tab
    $('#noEmailsSearchInput').on('input', filterNoEmailsCompanies);
    $('#noEmailsSortSelect').on('change', filterNoEmailsCompanies);
    $('#clearNoEmailsFiltersBtn').on('click', clearNoEmailsFilters);
    $('#refreshNoEmailsBtn').on('click', loadNoEmailsCompanies);
    $('#selectAllNoEmails').on('change', toggleSelectAllNoEmails);
    $('#bulkMigrateBtn').on('click', showBulkMigrateModal);
    $('#confirmMigrateBtn').on('click', handleBulkMigrate);
    $('#editReviewForm').on('submit', handleEditReview);

    // Event listeners for Review tab
    $('#reviewSearchInput').on('input', filterReviewCompanies);
    $('#reviewStatusFilter').on('change', filterReviewCompanies);
    $('#reviewSortSelect').on('change', filterReviewCompanies);
    $('#clearReviewFiltersBtn').on('click', clearReviewFilters);
    $('#refreshReviewBtn').on('click', loadReviewCompanies);

    function loadNoEmailsCompanies() {
        $('#noEmailsLoadingSpinner').show();
        $('#noEmailsTableBody').empty();
        $('#noEmailsNoResults').hide();

        $.get('/api/companies-no-emails')
            .done(function(data) {
                noEmailsCompanies = data;
                filterNoEmailsCompanies();
                updateStats();
            })
            .fail(function(xhr) {
                console.error('Error loading companies with no emails:', xhr.responseJSON);
                showAlert('Error loading companies: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
            })
            .always(function() {
                $('#noEmailsLoadingSpinner').hide();
            });
    }

    function loadReviewCompanies() {
        $('#reviewLoadingSpinner').show();
        $('#reviewTableBody').empty();
        $('#reviewNoResults').hide();

        $.get('/api/companies-no-emails/review')
            .done(function(data) {
                reviewCompanies = data;
                filterReviewCompanies();
                updateStats();
            })
            .fail(function(xhr) {
                console.error('Error loading review companies:', xhr.responseJSON);
                showAlert('Error loading review companies: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
            })
            .always(function() {
                $('#reviewLoadingSpinner').hide();
            });
    }

    function filterNoEmailsCompanies() {
        const searchTerm = $('#noEmailsSearchInput').val().toLowerCase();
        const sortBy = $('#noEmailsSortSelect').val();

        // Filter companies
        filteredNoEmailsCompanies = noEmailsCompanies.filter(company => {
            const nameMatch = company.name.toLowerCase().includes(searchTerm);
            const urlMatch = company.url.toLowerCase().includes(searchTerm);
            return nameMatch || urlMatch;
        });

        // Sort companies
        filteredNoEmailsCompanies.sort((a, b) => {
            switch(sortBy) {
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'name_desc':
                    return b.name.localeCompare(a.name);
                case 'url':
                    return a.url.localeCompare(b.url);
                case 'url_desc':
                    return b.url.localeCompare(a.url);
                case 'last_completed':
                    return new Date(b.last_completed || 0) - new Date(a.last_completed || 0);
                case 'pages_scraped':
                    return (a.total_pages_scraped || 0) - (b.total_pages_scraped || 0);
                case 'pages_scraped_desc':
                    return (b.total_pages_scraped || 0) - (a.total_pages_scraped || 0);
                default:
                    return 0;
            }
        });

        renderNoEmailsTable();
        updateStats();
    }

    function filterReviewCompanies() {
        const searchTerm = $('#reviewSearchInput').val().toLowerCase();
        const statusFilter = $('#reviewStatusFilter').val();
        const sortBy = $('#reviewSortSelect').val();

        // Filter companies
        filteredReviewCompanies = reviewCompanies.filter(company => {
            const nameMatch = company.name.toLowerCase().includes(searchTerm);
            const urlMatch = company.url.toLowerCase().includes(searchTerm);
            const statusMatch = !statusFilter || company.review_status === statusFilter;
            return nameMatch && urlMatch && statusMatch;
        });

        // Sort companies
        filteredReviewCompanies.sort((a, b) => {
            switch(sortBy) {
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'name_desc':
                    return b.name.localeCompare(a.name);
                case 'moved_at':
                    return new Date(b.moved_at) - new Date(a.moved_at);
                case 'review_status':
                    return (a.review_status || '').localeCompare(b.review_status || '');
                case 'reviewed_at':
                    return new Date(b.reviewed_at || 0) - new Date(a.reviewed_at || 0);
                default:
                    return 0;
            }
        });

        renderReviewTable();
        updateStats();
    }

    function renderNoEmailsTable() {
        const tbody = $('#noEmailsTableBody');
        tbody.empty();

        if (filteredNoEmailsCompanies.length === 0) {
            $('#noEmailsNoResults').show();
            return;
        }

        $('#noEmailsNoResults').hide();

        filteredNoEmailsCompanies.forEach(company => {
            const row = $(`
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input company-checkbox" value="${company.id}">
                    </td>
                    <td>
                        <strong>${escapeHtml(company.name)}</strong>
                    </td>
                    <td>
                        <a href="${escapeHtml(company.url)}" target="_blank" class="text-decoration-none">
                            ${escapeHtml(company.url)}
                            <i class="bi bi-box-arrow-up-right ms-1"></i>
                        </a>
                    </td>
                    <td>
                        <small class="text-muted">
                            ${formatDate(company.last_completed)}
                        </small>
                    </td>
                    <td>
                        <span class="badge bg-secondary">${company.job_count}</span>
                    </td>
                    <td>
                        <span class="badge bg-info">${company.total_pages_scraped || 0}</span>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-warning" onclick="migrateCompany(${company.id})" title="Migrate to Review">
                                <i class="bi bi-arrow-right"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `);
            tbody.append(row);
        });

        // Update checkbox event listeners
        $('.company-checkbox').on('change', updateBulkActions);
    }

    function renderReviewTable() {
        const tbody = $('#reviewTableBody');
        tbody.empty();

        if (filteredReviewCompanies.length === 0) {
            $('#reviewNoResults').show();
            return;
        }

        $('#reviewNoResults').hide();

        filteredReviewCompanies.forEach(company => {
            const statusBadge = getStatusBadge(company.review_status);
            const row = $(`
                <tr>
                    <td>
                        <strong>${escapeHtml(company.name)}</strong>
                    </td>
                    <td>
                        <a href="${escapeHtml(company.url)}" target="_blank" class="text-decoration-none">
                            ${escapeHtml(company.url)}
                            <i class="bi bi-box-arrow-up-right ms-1"></i>
                        </a>
                    </td>
                    <td>
                        <small class="text-muted">
                            ${formatDate(company.moved_at)}
                        </small>
                    </td>
                    <td>${statusBadge}</td>
                    <td>
                        <small class="text-muted">
                            ${company.reviewed_by || 'Not assigned'}
                        </small>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="editReviewCompany(${company.id})" title="Edit Review">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="deleteReviewCompany(${company.id})" title="Delete">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `);
            tbody.append(row);
        });
    }

    function updateStats() {
        $('#noEmailsCount').text(noEmailsCompanies.length);
        $('#noEmailsFilteredCount').text(filteredNoEmailsCompanies.length);

        const inReview = reviewCompanies.filter(c => c.review_status === 'pending' || c.review_status === 'in_progress').length;
        const reviewed = reviewCompanies.filter(c => c.review_status === 'completed').length;
        const totalPages = noEmailsCompanies.reduce((sum, c) => sum + (c.total_pages_scraped || 0), 0);

        $('#inReviewCount').text(inReview);
        $('#reviewedCount').text(reviewed);
        $('#totalPagesScraped').text(totalPages);
        $('#reviewFilteredCount').text(filteredReviewCompanies.length);
    }

    function clearNoEmailsFilters() {
        $('#noEmailsSearchInput').val('');
        $('#noEmailsSortSelect').val('last_completed');
        filterNoEmailsCompanies();
    }

    function clearReviewFilters() {
        $('#reviewSearchInput').val('');
        $('#reviewStatusFilter').val('');
        $('#reviewSortSelect').val('moved_at');
        filterReviewCompanies();
    }

    function toggleSelectAllNoEmails() {
        const isChecked = $('#selectAllNoEmails').is(':checked');
        $('.company-checkbox').prop('checked', isChecked);
        updateBulkActions();
    }

    function updateBulkActions() {
        const selectedCount = $('.company-checkbox:checked').length;
        $('#bulkMigrateBtn').prop('disabled', selectedCount === 0);
    }

    function showBulkMigrateModal() {
        const selectedIds = $('.company-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedIds.length === 0) {
            showAlert('Please select companies to migrate', 'warning');
            return;
        }

        const selectedCompanies = noEmailsCompanies.filter(c => selectedIds.includes(c.id.toString()));
        const companyList = selectedCompanies.map(c => `<li><strong>${escapeHtml(c.name)}</strong> - ${escapeHtml(c.url)}</li>`).join('');

        $('#migrateCompanyInfo').html(`
            <p><strong>Selected companies (${selectedCompanies.length}):</strong></p>
            <ul class="list-unstyled" style="max-height: 200px; overflow-y: auto;">
                ${companyList}
            </ul>
        `);

        $('#confirmMigrateBtn').data('company-ids', selectedIds);
        $('#migrateModal').modal('show');
    }

    function handleBulkMigrate() {
        const companyIds = $('#confirmMigrateBtn').data('company-ids');

        $.ajax({
            url: '/api/companies-no-emails/bulk-migrate',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ company_ids: companyIds })
        })
        .done(function(response) {
            showAlert(response.message, 'success');
            $('#migrateModal').modal('hide');
            loadNoEmailsCompanies();
            loadReviewCompanies();
            $('#selectAllNoEmails').prop('checked', false);
        })
        .fail(function(xhr) {
            showAlert('Error migrating companies: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
        });
    }

    function getStatusBadge(status) {
        const badges = {
            'pending': '<span class="badge bg-warning">Pending</span>',
            'in_progress': '<span class="badge bg-info">In Progress</span>',
            'completed': '<span class="badge bg-success">Completed</span>',
            'rejected': '<span class="badge bg-danger">Rejected</span>'
        };
        return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
    }

    function formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }

    function escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }

    function showAlert(message, type) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('main .container').prepend(alertHtml);

        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            $('.alert').alert('close');
        }, 5000);
    }

    // Global functions for button clicks
    window.migrateCompany = function(companyId) {
        $.ajax({
            url: `/api/companies/${companyId}/migrate`,
            method: 'POST'
        })
        .done(function(response) {
            showAlert(response.message, 'success');
            loadNoEmailsCompanies();
            loadReviewCompanies();
        })
        .fail(function(xhr) {
            showAlert('Error migrating company: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
        });
    };

    function handleEditReview(e) {
        e.preventDefault();

        const reviewId = $('#editReviewId').val();
        const reviewStatus = $('#editReviewStatus').val();
        const reviewNotes = $('#editReviewNotes').val().trim();
        const reviewedBy = $('#editReviewedBy').val().trim();
        const actionTaken = $('#editActionTaken').val();

        $.ajax({
            url: `/api/companies-no-emails/review/${reviewId}`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify({
                review_status: reviewStatus,
                review_notes: reviewNotes,
                reviewed_by: reviewedBy,
                action_taken: actionTaken
            })
        })
        .done(function(response) {
            showAlert(response.message, 'success');
            $('#editReviewModal').modal('hide');
            loadReviewCompanies();
        })
        .fail(function(xhr) {
            showAlert('Error updating review: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
        });
    }

    window.editReviewCompany = function(reviewId) {
        const company = reviewCompanies.find(c => c.id === reviewId);
        if (company) {
            $('#editReviewId').val(company.id);
            $('#editReviewCompanyName').val(company.name);
            $('#editReviewCompanyUrl').val(company.url);
            $('#editReviewStatus').val(company.review_status || 'pending');
            $('#editReviewNotes').val(company.review_notes || '');
            $('#editReviewedBy').val(company.reviewed_by || '');
            $('#editActionTaken').val(company.action_taken || '');
            $('#editReviewModal').modal('show');
        }
    };

    window.deleteReviewCompany = function(reviewId) {
        if (confirm('Are you sure you want to delete this review entry?')) {
            $.ajax({
                url: `/api/companies-no-emails/review/${reviewId}`,
                method: 'DELETE'
            })
            .done(function(response) {
                showAlert(response.message, 'success');
                loadReviewCompanies();
            })
            .fail(function(xhr) {
                showAlert('Error deleting review company: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
            });
        }
    };
});
