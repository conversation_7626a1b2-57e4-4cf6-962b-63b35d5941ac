$(document).ready(function() {
    let noEmailsCompanies = [];
    let reviewCompanies = [];
    let noEmailsPagination = {};
    let reviewPagination = {};

    // Initialize pagination managers
    const noEmailsPaginationManager = new window.PaginationUtils.PaginationManager({
        page: 1,
        perPage: 25,
        onStateChange: loadNoEmailsCompanies
    });

    const reviewPaginationManager = new window.PaginationUtils.PaginationManager({
        page: 1,
        perPage: 25,
        onStateChange: loadReviewCompanies
    });

    // Load data on page load
    loadNoEmailsCompanies();
    loadReviewCompanies();

    // Event listeners for No Emails tab
    $('#noEmailsSearchInput').on('input', debounce(function() {
        noEmailsPaginationManager.setSearch($(this).val());
    }, 300));
    $('#noEmailsSortSelect').on('change', function() {
        noEmailsPaginationManager.setSortBy($(this).val());
    });
    $('#clearNoEmailsFiltersBtn').on('click', clearNoEmailsFilters);
    $('#refreshNoEmailsBtn').on('click', loadNoEmailsCompanies);
    $('#selectAllNoEmails').on('change', toggleSelectAllNoEmails);
    $('#bulkMigrateBtn').on('click', showBulkMigrateModal);
    $('#confirmMigrateBtn').on('click', handleBulkMigrate);
    $('#editReviewForm').on('submit', handleEditReview);

    // Event listeners for Review tab
    $('#reviewSearchInput').on('input', debounce(function() {
        reviewPaginationManager.setSearch($(this).val());
    }, 300));
    $('#reviewStatusFilter').on('change', function() {
        reviewPaginationManager.setFilter('status', $(this).val());
    });
    $('#reviewSortSelect').on('change', function() {
        reviewPaginationManager.setSortBy($(this).val());
    });
    $('#clearReviewFiltersBtn').on('click', clearReviewFilters);
    $('#refreshReviewBtn').on('click', loadReviewCompanies);

    function loadNoEmailsCompanies() {
        $('#noEmailsLoadingSpinner').show();
        $('#noEmailsTableBody').empty();
        $('#noEmailsNoResults').hide();
        $('#noEmailsPaginationContainer').hide();

        const params = noEmailsPaginationManager.getParams();

        $.get('/api/companies-no-emails', params)
            .done(function(data) {
                noEmailsCompanies = data.companies || [];
                noEmailsPagination = data.pagination || {};
                renderNoEmailsTable();
                updateNoEmailsPagination();
                updateStats();
            })
            .fail(function(xhr) {
                console.error('Error loading companies with no emails:', xhr.responseJSON);
                showAlert('Error loading companies: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
            })
            .always(function() {
                $('#noEmailsLoadingSpinner').hide();
            });
    }

    function loadReviewCompanies() {
        $('#reviewLoadingSpinner').show();
        $('#reviewTableBody').empty();
        $('#reviewNoResults').hide();
        $('#reviewPaginationContainer').hide();

        const params = reviewPaginationManager.getParams();

        $.get('/api/companies-no-emails/review', params)
            .done(function(data) {
                reviewCompanies = data.companies || [];
                reviewPagination = data.pagination || {};
                renderReviewTable();
                updateReviewPagination();
                updateStats();
            })
            .fail(function(xhr) {
                console.error('Error loading review companies:', xhr.responseJSON);
                showAlert('Error loading review companies: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
            })
            .always(function() {
                $('#reviewLoadingSpinner').hide();
            });
    }

    function updateNoEmailsPagination() {
        if (noEmailsPagination.total > 0) {
            $('#noEmailsPaginationContainer').show();

            // Create pagination controls
            window.PaginationUtils.createPaginationControls(
                noEmailsPagination,
                'noEmailsPagination',
                (page) => noEmailsPaginationManager.setPage(page)
            );

            // Create pagination info
            window.PaginationUtils.createPaginationInfo(
                noEmailsPagination,
                'noEmailsPaginationInfo',
                'companies'
            );

            // Create per-page selector
            window.PaginationUtils.createPerPageSelector(
                noEmailsPagination.per_page,
                'noEmailsPerPageSelector',
                (perPage) => noEmailsPaginationManager.setPerPage(perPage)
            );
        } else {
            $('#noEmailsPaginationContainer').hide();
        }
    }

    function updateReviewPagination() {
        if (reviewPagination.total > 0) {
            $('#reviewPaginationContainer').show();

            // Create pagination controls
            window.PaginationUtils.createPaginationControls(
                reviewPagination,
                'reviewPagination',
                (page) => reviewPaginationManager.setPage(page)
            );

            // Create pagination info
            window.PaginationUtils.createPaginationInfo(
                reviewPagination,
                'reviewPaginationInfo',
                'companies'
            );

            // Create per-page selector
            window.PaginationUtils.createPerPageSelector(
                reviewPagination.per_page,
                'reviewPerPageSelector',
                (perPage) => reviewPaginationManager.setPerPage(perPage)
            );
        } else {
            $('#reviewPaginationContainer').hide();
        }
    }

    function renderNoEmailsTable() {
        const tbody = $('#noEmailsTableBody');
        tbody.empty();

        if (noEmailsCompanies.length === 0) {
            $('#noEmailsNoResults').show();
            return;
        }

        $('#noEmailsNoResults').hide();

        noEmailsCompanies.forEach(company => {
            const row = $(`
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input company-checkbox" value="${company.id}">
                    </td>
                    <td>
                        <strong>${escapeHtml(company.name)}</strong>
                    </td>
                    <td>
                        <a href="${escapeHtml(company.url)}" target="_blank" class="text-decoration-none">
                            ${escapeHtml(company.url)}
                            <i class="bi bi-box-arrow-up-right ms-1"></i>
                        </a>
                    </td>
                    <td>
                        <small class="text-muted">
                            ${formatDate(company.last_completed)}
                        </small>
                    </td>
                    <td>
                        <span class="badge bg-secondary">${company.job_count}</span>
                    </td>
                    <td>
                        <span class="badge bg-info">${company.total_pages_scraped || 0}</span>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-warning" onclick="migrateCompany(${company.id})" title="Migrate to Review">
                                <i class="bi bi-arrow-right"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `);
            tbody.append(row);
        });

        // Update checkbox event listeners
        $('.company-checkbox').on('change', updateBulkActions);
    }

    function renderReviewTable() {
        const tbody = $('#reviewTableBody');
        tbody.empty();

        if (reviewCompanies.length === 0) {
            $('#reviewNoResults').show();
            return;
        }

        $('#reviewNoResults').hide();

        reviewCompanies.forEach(company => {
            const statusBadge = getStatusBadge(company.review_status);
            const row = $(`
                <tr>
                    <td>
                        <strong>${escapeHtml(company.name)}</strong>
                    </td>
                    <td>
                        <a href="${escapeHtml(company.url)}" target="_blank" class="text-decoration-none">
                            ${escapeHtml(company.url)}
                            <i class="bi bi-box-arrow-up-right ms-1"></i>
                        </a>
                    </td>
                    <td>
                        <small class="text-muted">
                            ${formatDate(company.moved_at)}
                        </small>
                    </td>
                    <td>${statusBadge}</td>
                    <td>
                        <small class="text-muted">
                            ${company.reviewed_by || 'Not assigned'}
                        </small>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="editReviewCompany(${company.id})" title="Edit Review">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="deleteReviewCompany(${company.id})" title="Delete">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `);
            tbody.append(row);
        });
    }

    function updateStats() {
        $('#noEmailsCount').text(noEmailsPagination.total || 0);
        $('#noEmailsFilteredCount').text(noEmailsPagination.total || 0);

        // For review stats, we need to make a separate call or calculate from current data
        const inReview = reviewCompanies.filter(c => c.review_status === 'pending' || c.review_status === 'in_progress').length;
        const reviewed = reviewCompanies.filter(c => c.review_status === 'completed').length;
        const totalPages = noEmailsCompanies.reduce((sum, c) => sum + (c.total_pages_scraped || 0), 0);

        $('#inReviewCount').text(inReview);
        $('#reviewedCount').text(reviewed);
        $('#totalPagesScraped').text(totalPages);
        $('#reviewFilteredCount').text(reviewPagination.total || 0);
    }

    function clearNoEmailsFilters() {
        $('#noEmailsSearchInput').val('');
        $('#noEmailsSortSelect').val('last_completed');
        noEmailsPaginationManager.reset();
    }

    function clearReviewFilters() {
        $('#reviewSearchInput').val('');
        $('#reviewStatusFilter').val('');
        $('#reviewSortSelect').val('moved_at');
        reviewPaginationManager.reset();
    }

    // Debounce function for search input
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function toggleSelectAllNoEmails() {
        const isChecked = $('#selectAllNoEmails').is(':checked');
        $('.company-checkbox').prop('checked', isChecked);
        updateBulkActions();
    }

    function updateBulkActions() {
        const selectedCount = $('.company-checkbox:checked').length;
        $('#bulkMigrateBtn').prop('disabled', selectedCount === 0);
    }

    function showBulkMigrateModal() {
        const selectedIds = $('.company-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedIds.length === 0) {
            showAlert('Please select companies to migrate', 'warning');
            return;
        }

        const selectedCompanies = noEmailsCompanies.filter(c => selectedIds.includes(c.id.toString()));
        const companyList = selectedCompanies.map(c => `<li><strong>${escapeHtml(c.name)}</strong> - ${escapeHtml(c.url)}</li>`).join('');

        $('#migrateCompanyInfo').html(`
            <p><strong>Selected companies (${selectedCompanies.length}):</strong></p>
            <ul class="list-unstyled" style="max-height: 200px; overflow-y: auto;">
                ${companyList}
            </ul>
        `);

        $('#confirmMigrateBtn').data('company-ids', selectedIds);
        $('#migrateModal').modal('show');
    }

    function handleBulkMigrate() {
        const companyIds = $('#confirmMigrateBtn').data('company-ids');

        $.ajax({
            url: '/api/companies-no-emails/bulk-migrate',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ company_ids: companyIds })
        })
        .done(function(response) {
            showAlert(response.message, 'success');
            $('#migrateModal').modal('hide');
            loadNoEmailsCompanies();
            loadReviewCompanies();
            $('#selectAllNoEmails').prop('checked', false);
        })
        .fail(function(xhr) {
            showAlert('Error migrating companies: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
        });
    }

    function getStatusBadge(status) {
        const badges = {
            'pending': '<span class="badge bg-warning">Pending</span>',
            'in_progress': '<span class="badge bg-info">In Progress</span>',
            'completed': '<span class="badge bg-success">Completed</span>',
            'rejected': '<span class="badge bg-danger">Rejected</span>'
        };
        return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
    }

    function formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }

    function escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }

    function showAlert(message, type) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('main .container').prepend(alertHtml);

        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            $('.alert').alert('close');
        }, 5000);
    }

    // Global functions for button clicks
    window.migrateCompany = function(companyId) {
        $.ajax({
            url: `/api/companies/${companyId}/migrate`,
            method: 'POST'
        })
        .done(function(response) {
            showAlert(response.message, 'success');
            loadNoEmailsCompanies();
            loadReviewCompanies();
        })
        .fail(function(xhr) {
            showAlert('Error migrating company: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
        });
    };

    function handleEditReview(e) {
        e.preventDefault();

        const reviewId = $('#editReviewId').val();
        const reviewStatus = $('#editReviewStatus').val();
        const reviewNotes = $('#editReviewNotes').val().trim();
        const reviewedBy = $('#editReviewedBy').val().trim();
        const actionTaken = $('#editActionTaken').val();

        $.ajax({
            url: `/api/companies-no-emails/review/${reviewId}`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify({
                review_status: reviewStatus,
                review_notes: reviewNotes,
                reviewed_by: reviewedBy,
                action_taken: actionTaken
            })
        })
        .done(function(response) {
            showAlert(response.message, 'success');
            $('#editReviewModal').modal('hide');
            loadReviewCompanies();
        })
        .fail(function(xhr) {
            showAlert('Error updating review: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
        });
    }

    window.editReviewCompany = function(reviewId) {
        const company = reviewCompanies.find(c => c.id === reviewId);
        if (company) {
            $('#editReviewId').val(company.id);
            $('#editReviewCompanyName').val(company.name);
            $('#editReviewCompanyUrl').val(company.url);
            $('#editReviewStatus').val(company.review_status || 'pending');
            $('#editReviewNotes').val(company.review_notes || '');
            $('#editReviewedBy').val(company.reviewed_by || '');
            $('#editActionTaken').val(company.action_taken || '');
            $('#editReviewModal').modal('show');
        }
    };

    window.deleteReviewCompany = function(reviewId) {
        if (confirm('Are you sure you want to delete this review entry?')) {
            $.ajax({
                url: `/api/companies-no-emails/review/${reviewId}`,
                method: 'DELETE'
            })
            .done(function(response) {
                showAlert(response.message, 'success');
                loadReviewCompanies();
            })
            .fail(function(xhr) {
                showAlert('Error deleting review company: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
            });
        }
    };
});
