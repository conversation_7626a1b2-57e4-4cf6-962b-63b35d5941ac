$(document).ready(function() {
    let emails = [];
    let pagination = {};
    let companies = [];
    let selectedEmailIds = [];

    // Initialize pagination manager
    const paginationManager = new window.PaginationUtils.PaginationManager({
        page: 1,
        perPage: 25,
        onStateChange: loadEmails
    });

    // Load data on page load
    loadEmails();
    loadCompanies();

    // Event listeners
    $('#emailSearchInput').on('input', debounce(function() {
        paginationManager.setSearch($(this).val());
    }, 300));
    $('#companyFilter').on('change', function() {
        paginationManager.setFilter('company_id', $(this).val());
    });
    $('#emailSortSelect').on('change', function() {
        paginationManager.setSortBy($(this).val());
    });
    $('#clearEmailFiltersBtn').on('click', clearFilters);
    $('#refreshEmailsBtn').on('click', loadEmails);
    $('#selectAllEmails').on('change', handleSelectAll);
    $('#deleteSelectedBtn').on('click', handleBulkDelete);
    $('#exportFilteredBtn').on('click', exportFilteredEmails);

    // Form submissions
    $('#addEmailForm').on('submit', handleAddEmail);
    $('#editEmailForm').on('submit', handleEditEmail);
    $('#confirmDeleteEmailBtn').on('click', handleDeleteEmail);
    $('#confirmBulkDeleteBtn').on('click', confirmBulkDelete);

    function loadEmails() {
        $('#emailLoadingSpinner').show();
        $('#emailsTableBody').empty();
        $('#noEmailResults').hide();
        $('#emailsPaginationContainer').hide();

        const params = paginationManager.getParams();

        $.get('/api/emails', params)
            .done(function(data) {
                emails = data.emails || [];
                pagination = data.pagination || {};
                renderEmailsTable();
                updatePagination();
                updateStats();
                clearSelection();
            })
            .fail(function(xhr) {
                console.error('Error loading emails:', xhr.responseJSON);
                showAlert('Error loading emails: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
            })
            .always(function() {
                $('#emailLoadingSpinner').hide();
            });
    }

    function loadCompanies() {
        $.get('/api/companies')
            .done(function(data) {
                companies = data;
                populateCompanySelects();
            })
            .fail(function(xhr) {
                console.error('Error loading companies:', xhr.responseJSON);
            });
    }

    function populateCompanySelects() {
        const selects = ['#companyFilter', '#editEmailCompany', '#newEmailCompany'];

        selects.forEach(selectId => {
            const $select = $(selectId);
            const currentValue = $select.val();

            // Clear existing options (except first one for filter)
            if (selectId === '#companyFilter') {
                $select.find('option:not(:first)').remove();
            } else {
                $select.find('option:not(:first)').remove();
            }

            companies.forEach(company => {
                $select.append(`<option value="${company.id}">${escapeHtml(company.name)}</option>`);
            });

            // Restore previous value if it exists
            if (currentValue) {
                $select.val(currentValue);
            }
        });
    }

    function updatePagination() {
        if (pagination.total > 0) {
            $('#emailsPaginationContainer').show();

            // Create pagination controls
            window.PaginationUtils.createPaginationControls(
                pagination,
                'emailsPagination',
                (page) => paginationManager.setPage(page)
            );

            // Create pagination info
            window.PaginationUtils.createPaginationInfo(
                pagination,
                'emailsPaginationInfo',
                'emails'
            );

            // Create per-page selector
            window.PaginationUtils.createPerPageSelector(
                pagination.per_page,
                'emailsPerPageSelector',
                (perPage) => paginationManager.setPerPage(perPage)
            );
        } else {
            $('#emailsPaginationContainer').hide();
        }
    }

    function renderEmailsTable() {
        const tbody = $('#emailsTableBody');
        tbody.empty();

        if (emails.length === 0) {
            $('#noEmailResults').show();
            return;
        }

        $('#noEmailResults').hide();

        emails.forEach(email => {
            const row = $(`
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input email-checkbox" value="${email.id}">
                    </td>
                    <td>
                        <strong>${escapeHtml(email.email_address)}</strong>
                    </td>
                    <td>
                        ${email.company_name ? `<span class="badge bg-primary">${escapeHtml(email.company_name)}</span>` : '<span class="text-muted">No Company</span>'}
                    </td>
                    <td>
                        ${email.found_on_page ? `<small><a href="${escapeHtml(email.found_on_page)}" target="_blank" class="text-decoration-none">${escapeHtml(email.found_on_page)}</a></small>` : '-'}
                    </td>
                    <td>
                        <small class="text-muted">
                            ${formatDate(email.created_at)}
                        </small>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-secondary" onclick="editEmail(${email.id})" title="Edit">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="deleteEmail(${email.id})" title="Delete">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `);
            tbody.append(row);
        });

        // Add event listeners for checkboxes
        $('.email-checkbox').on('change', updateSelection);
    }

    function updateStats() {
        $('#totalEmails').text(pagination.total || 0);
        $('#filteredEmails').text(pagination.total || 0);
    }

    function clearFilters() {
        $('#emailSearchInput').val('');
        $('#companyFilter').val('');
        $('#emailSortSelect').val('created_at');
        paginationManager.reset();
    }

    // Debounce function for search input
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function updateSelection() {
        selectedEmailIds = $('.email-checkbox:checked').map(function() {
            return parseInt($(this).val());
        }).get();

        const currentPageEmails = emails.length;
        $('#selectAllEmails').prop('indeterminate', selectedEmailIds.length > 0 && selectedEmailIds.length < currentPageEmails);
        $('#selectAllEmails').prop('checked', selectedEmailIds.length === currentPageEmails && currentPageEmails > 0);
        $('#deleteSelectedBtn').prop('disabled', selectedEmailIds.length === 0);
    }

    function handleSelectAll() {
        const isChecked = $('#selectAllEmails').prop('checked');
        $('.email-checkbox').prop('checked', isChecked);
        updateSelection();
    }

    function clearSelection() {
        selectedEmailIds = [];
        $('#selectAllEmails').prop('checked', false).prop('indeterminate', false);
        $('#deleteSelectedBtn').prop('disabled', true);
    }

    function handleAddEmail(e) {
        e.preventDefault();

        const emailAddress = $('#newEmailAddress').val().trim();
        const companyId = $('#newEmailCompany').val() || null;
        const foundOnPage = $('#newEmailPage').val().trim();

        if (!emailAddress) {
            showAlert('Please enter an email address', 'warning');
            return;
        }

        $.ajax({
            url: '/api/emails',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                email_address: emailAddress,
                company_id: companyId,
                found_on_page: foundOnPage
            })
        })
        .done(function(response) {
            showAlert(response.message, 'success');
            $('#addEmailModal').modal('hide');
            $('#addEmailForm')[0].reset();
            loadEmails();
        })
        .fail(function(xhr) {
            showAlert('Error adding email: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
        });
    }

    function handleEditEmail(e) {
        e.preventDefault();

        const emailId = $('#editEmailId').val();
        const emailAddress = $('#editEmailAddress').val().trim();
        const foundOnPage = $('#editEmailPage').val().trim();

        if (!emailAddress) {
            showAlert('Please enter an email address', 'warning');
            return;
        }

        $.ajax({
            url: `/api/emails/${emailId}`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify({
                email_address: emailAddress,
                found_on_page: foundOnPage
            })
        })
        .done(function(response) {
            showAlert(response.message, 'success');
            $('#editEmailModal').modal('hide');
            loadEmails();
        })
        .fail(function(xhr) {
            showAlert('Error updating email: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
        });
    }

    function handleDeleteEmail() {
        const emailId = $('#confirmDeleteEmailBtn').data('email-id');

        $.ajax({
            url: `/api/emails/${emailId}`,
            method: 'DELETE'
        })
        .done(function(response) {
            showAlert(response.message, 'success');
            $('#deleteEmailModal').modal('hide');
            loadEmails();
        })
        .fail(function(xhr) {
            showAlert('Error deleting email: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
        });
    }

    function handleBulkDelete() {
        if (selectedEmailIds.length === 0) return;

        $('#bulkDeleteCount').text(selectedEmailIds.length);
        $('#bulkDeleteModal').modal('show');
    }

    function confirmBulkDelete() {
        $.ajax({
            url: '/api/emails/bulk-delete',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                email_ids: selectedEmailIds
            })
        })
        .done(function(response) {
            showAlert(response.message, 'success');
            $('#bulkDeleteModal').modal('hide');
            loadEmails();
        })
        .fail(function(xhr) {
            showAlert('Error deleting emails: ' + (xhr.responseJSON?.error || 'Unknown error'), 'danger');
        });
    }

    function exportFilteredEmails() {
        // Create CSV content from filtered emails
        let csvContent = 'Email Address,Company,Found On Page,Date Found\n';

        filteredEmails.forEach(email => {
            const company = email.company_name || '';
            const foundOn = email.found_on_page || '';
            const date = formatDate(email.created_at);

            csvContent += `"${email.email_address}","${company}","${foundOn}","${date}"\n`;
        });

        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `filtered_emails_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    // Global functions for button clicks
    window.editEmail = function(emailId) {
        const email = emails.find(e => e.id === emailId);
        if (email) {
            $('#editEmailId').val(email.id);
            $('#editEmailAddress').val(email.email_address);
            $('#editEmailPage').val(email.found_on_page || '');
            $('#editEmailModal').modal('show');
        }
    };

    window.deleteEmail = function(emailId) {
        const email = emails.find(e => e.id === emailId);
        if (email) {
            $('#deleteEmailInfo').html(`
                <strong>Email:</strong> ${escapeHtml(email.email_address)}<br>
                <strong>Company:</strong> ${email.company_name || 'No Company'}<br>
                <strong>Found On:</strong> ${email.found_on_page || 'Not specified'}
            `);
            $('#confirmDeleteEmailBtn').data('email-id', emailId);
            $('#deleteEmailModal').modal('show');
        }
    };

    // Utility functions
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function formatDate(dateString) {
        return new Date(dateString).toLocaleDateString() + ' ' + new Date(dateString).toLocaleTimeString();
    }

    function showAlert(message, type) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('.container').prepend(alertHtml);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            $('.alert').first().alert('close');
        }, 5000);
    }
});
