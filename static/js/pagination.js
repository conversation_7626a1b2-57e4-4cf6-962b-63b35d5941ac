// Pagination utility functions for <PERSON>ail Scraper

/**
 * Create pagination controls HTML
 * @param {Object} pagination - Pagination object from API response
 * @param {string} containerId - ID of the container to render pagination
 * @param {Function} onPageChange - Callback function when page changes
 */
function createPaginationControls(pagination, containerId, onPageChange) {
    const container = document.getElementById(containerId);
    if (!container) return;

    const { page, pages, has_prev, has_next, total, per_page } = pagination;
    
    // Clear existing pagination
    container.innerHTML = '';
    
    if (pages <= 1) {
        return; // No pagination needed
    }

    const nav = document.createElement('nav');
    nav.setAttribute('aria-label', 'Table pagination');
    
    const ul = document.createElement('ul');
    ul.className = 'pagination pagination-sm justify-content-center mb-0';
    
    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${!has_prev ? 'disabled' : ''}`;
    const prevLink = document.createElement('a');
    prevLink.className = 'page-link';
    prevLink.href = '#';
    prevLink.innerHTML = '<i class="bi bi-chevron-left"></i>';
    prevLink.setAttribute('aria-label', 'Previous');
    if (has_prev) {
        prevLink.addEventListener('click', (e) => {
            e.preventDefault();
            onPageChange(page - 1);
        });
    }
    prevLi.appendChild(prevLink);
    ul.appendChild(prevLi);
    
    // Page numbers
    const startPage = Math.max(1, page - 2);
    const endPage = Math.min(pages, page + 2);
    
    // First page if not in range
    if (startPage > 1) {
        const firstLi = createPageItem(1, page, onPageChange);
        ul.appendChild(firstLi);
        
        if (startPage > 2) {
            const ellipsisLi = document.createElement('li');
            ellipsisLi.className = 'page-item disabled';
            const ellipsisSpan = document.createElement('span');
            ellipsisSpan.className = 'page-link';
            ellipsisSpan.textContent = '...';
            ellipsisLi.appendChild(ellipsisSpan);
            ul.appendChild(ellipsisLi);
        }
    }
    
    // Page range
    for (let i = startPage; i <= endPage; i++) {
        const pageLi = createPageItem(i, page, onPageChange);
        ul.appendChild(pageLi);
    }
    
    // Last page if not in range
    if (endPage < pages) {
        if (endPage < pages - 1) {
            const ellipsisLi = document.createElement('li');
            ellipsisLi.className = 'page-item disabled';
            const ellipsisSpan = document.createElement('span');
            ellipsisSpan.className = 'page-link';
            ellipsisSpan.textContent = '...';
            ellipsisLi.appendChild(ellipsisSpan);
            ul.appendChild(ellipsisLi);
        }
        
        const lastLi = createPageItem(pages, page, onPageChange);
        ul.appendChild(lastLi);
    }
    
    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${!has_next ? 'disabled' : ''}`;
    const nextLink = document.createElement('a');
    nextLink.className = 'page-link';
    nextLink.href = '#';
    nextLink.innerHTML = '<i class="bi bi-chevron-right"></i>';
    nextLink.setAttribute('aria-label', 'Next');
    if (has_next) {
        nextLink.addEventListener('click', (e) => {
            e.preventDefault();
            onPageChange(page + 1);
        });
    }
    nextLi.appendChild(nextLink);
    ul.appendChild(nextLi);
    
    nav.appendChild(ul);
    container.appendChild(nav);
}

/**
 * Create a page item for pagination
 * @param {number} pageNum - Page number
 * @param {number} currentPage - Current active page
 * @param {Function} onPageChange - Callback function
 * @returns {HTMLElement} - List item element
 */
function createPageItem(pageNum, currentPage, onPageChange) {
    const li = document.createElement('li');
    li.className = `page-item ${pageNum === currentPage ? 'active' : ''}`;
    
    const link = document.createElement('a');
    link.className = 'page-link';
    link.href = '#';
    link.textContent = pageNum;
    
    if (pageNum !== currentPage) {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            onPageChange(pageNum);
        });
    }
    
    li.appendChild(link);
    return li;
}

/**
 * Create pagination info text
 * @param {Object} pagination - Pagination object from API response
 * @param {string} containerId - ID of the container to render info
 * @param {string} itemName - Name of items (e.g., "companies", "emails")
 */
function createPaginationInfo(pagination, containerId, itemName = 'items') {
    const container = document.getElementById(containerId);
    if (!container) return;

    const { page, per_page, total } = pagination;
    
    if (total === 0) {
        container.textContent = `No ${itemName} found`;
        return;
    }
    
    const start = (page - 1) * per_page + 1;
    const end = Math.min(page * per_page, total);
    
    container.textContent = `Showing ${start}-${end} of ${total} ${itemName}`;
}

/**
 * Create per-page selector
 * @param {number} currentPerPage - Current per page value
 * @param {string} containerId - ID of the container to render selector
 * @param {Function} onPerPageChange - Callback function when per page changes
 */
function createPerPageSelector(currentPerPage, containerId, onPerPageChange) {
    const container = document.getElementById(containerId);
    if (!container) return;

    const select = document.createElement('select');
    select.className = 'form-select form-select-sm';
    select.style.width = 'auto';
    
    const options = [10, 25, 50, 100];
    options.forEach(value => {
        const option = document.createElement('option');
        option.value = value;
        option.textContent = `${value} per page`;
        option.selected = value === currentPerPage;
        select.appendChild(option);
    });
    
    select.addEventListener('change', (e) => {
        onPerPageChange(parseInt(e.target.value));
    });
    
    container.innerHTML = '';
    container.appendChild(select);
}

/**
 * Pagination state manager class
 */
class PaginationManager {
    constructor(options = {}) {
        this.page = options.page || 1;
        this.perPage = options.perPage || 25;
        this.search = options.search || '';
        this.sortBy = options.sortBy || '';
        this.filters = options.filters || {};
        this.onStateChange = options.onStateChange || (() => {});
    }
    
    setPage(page) {
        this.page = page;
        this.onStateChange();
    }
    
    setPerPage(perPage) {
        this.perPage = perPage;
        this.page = 1; // Reset to first page
        this.onStateChange();
    }
    
    setSearch(search) {
        this.search = search;
        this.page = 1; // Reset to first page
        this.onStateChange();
    }
    
    setSortBy(sortBy) {
        this.sortBy = sortBy;
        this.page = 1; // Reset to first page
        this.onStateChange();
    }
    
    setFilter(key, value) {
        this.filters[key] = value;
        this.page = 1; // Reset to first page
        this.onStateChange();
    }
    
    getParams() {
        const params = {
            page: this.page,
            per_page: this.perPage
        };
        
        if (this.search) params.search = this.search;
        if (this.sortBy) params.sort_by = this.sortBy;
        
        // Add filters
        Object.keys(this.filters).forEach(key => {
            if (this.filters[key]) {
                params[key] = this.filters[key];
            }
        });
        
        return params;
    }
    
    reset() {
        this.page = 1;
        this.search = '';
        this.sortBy = '';
        this.filters = {};
        this.onStateChange();
    }
}

// Export for use in other files
window.PaginationUtils = {
    createPaginationControls,
    createPaginationInfo,
    createPerPageSelector,
    PaginationManager
};
