{% extends "base.html" %}

{% block title %}Manage Emails - <PERSON><PERSON>raper{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-envelope"></i> Manage Emails
        </h1>
    </div>
</div>

<!-- Controls Section -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="emailSearchInput" class="form-label">Search Emails</label>
                        <input type="text" class="form-control" id="emailSearchInput" placeholder="Search by email address...">
                    </div>
                    <div class="col-md-4">
                        <label for="companyFilter" class="form-label">Filter by Company</label>
                        <select class="form-select" id="companyFilter">
                            <option value="">All Companies</option>
                            <!-- Options will be populated by JavaScript -->
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="emailSortSelect" class="form-label">Sort By</label>
                        <select class="form-select" id="emailSortSelect">
                            <option value="email">Email (A-Z)</option>
                            <option value="email_desc">Email (Z-A)</option>
                            <option value="company">Company</option>
                            <option value="created_at">Newest First</option>
                            <option value="created_at_desc">Oldest First</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-secondary me-2" id="clearEmailFiltersBtn">
                            <i class="bi bi-x-circle"></i> Clear
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">Quick Stats</h6>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 text-primary mb-0" id="totalEmails">-</div>
                        <small class="text-muted">Total Emails</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 text-success mb-0" id="filteredEmails">-</div>
                        <small class="text-muted">Filtered Results</small>
                    </div>
                </div>
                <div class="mt-3">
                    <button type="button" class="btn btn-outline-primary btn-sm w-100" id="exportFilteredBtn">
                        <i class="bi bi-download"></i> Export Filtered Results
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Emails Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i> Emails List
                </h5>
                <div>
                    <button type="button" class="btn btn-outline-danger btn-sm me-2" id="deleteSelectedBtn" disabled>
                        <i class="bi bi-trash"></i> Delete Selected
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" id="refreshEmailsBtn">
                        <i class="bi bi-arrow-clockwise"></i> Refresh
                    </button>
                </div>
            </div>
            <div class="card-body table-with-pagination">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="emailsTable">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAllEmails" class="form-check-input">
                                </th>
                                <th>Email Address</th>
                                <th>Company</th>
                                <th>Found On Page</th>
                                <th>Date Found</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="emailsTableBody">
                            <!-- Emails will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
                <div id="noEmailResults" class="text-center py-4" style="display: none;">
                    <i class="bi bi-search text-muted" style="font-size: 3rem;"></i>
                    <p class="text-muted mt-2">No emails found matching your criteria.</p>
                </div>
                <div id="emailLoadingSpinner" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

                <!-- Pagination Container -->
                <div class="pagination-container" id="emailsPaginationContainer" style="display: none;">
                    <div class="pagination-info">
                        <span id="emailsPaginationInfo"></span>
                    </div>
                    <div class="pagination-controls">
                        <div class="pagination-per-page">
                            <span>Show:</span>
                            <div id="emailsPerPageSelector"></div>
                        </div>
                        <div id="emailsPagination"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Email Modal -->
<div class="modal fade" id="editEmailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Email</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editEmailForm">
                <div class="modal-body">
                    <input type="hidden" id="editEmailId">
                    <div class="mb-3">
                        <label for="editEmailAddress" class="form-label">Email Address *</label>
                        <input type="email" class="form-control" id="editEmailAddress" required>
                    </div>
                    <div class="mb-3">
                        <label for="editEmailCompany" class="form-label">Associated Company</label>
                        <select class="form-select" id="editEmailCompany">
                            <option value="">No Company</option>
                            <!-- Options will be populated by JavaScript -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editEmailPage" class="form-label">Found On Page</label>
                        <input type="url" class="form-control" id="editEmailPage" placeholder="https://example.com/page">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Email Confirmation Modal -->
<div class="modal fade" id="deleteEmailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this email?</p>
                <div id="deleteEmailInfo">
                    <!-- Email info will be populated by JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteEmailBtn">
                    <i class="bi bi-trash"></i> Delete Email
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Confirmation Modal -->
<div class="modal fade" id="bulkDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Bulk Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <span id="bulkDeleteCount">0</span> selected emails?</p>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Warning:</strong> This action cannot be undone.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmBulkDeleteBtn">
                    <i class="bi bi-trash"></i> Delete Selected
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Manual Email Modal -->
<div class="modal fade" id="addEmailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Email Manually</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addEmailForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="newEmailAddress" class="form-label">Email Address *</label>
                        <input type="email" class="form-control" id="newEmailAddress" required>
                    </div>
                    <div class="mb-3">
                        <label for="newEmailCompany" class="form-label">Associated Company</label>
                        <select class="form-select" id="newEmailCompany">
                            <option value="">No Company</option>
                            <!-- Options will be populated by JavaScript -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="newEmailPage" class="form-label">Found On Page</label>
                        <input type="url" class="form-control" id="newEmailPage" placeholder="https://example.com/page">
                        <div class="form-text">Optional: URL where this email was found</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add Email
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Floating Action Button -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
    <button type="button" class="btn btn-primary btn-lg rounded-circle" data-bs-toggle="modal" data-bs-target="#addEmailModal" title="Add Email Manually">
        <i class="bi bi-plus-lg"></i>
    </button>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/pagination.js') }}"></script>
<script src="{{ url_for('static', filename='js/manage_emails.js') }}"></script>
{% endblock %}
