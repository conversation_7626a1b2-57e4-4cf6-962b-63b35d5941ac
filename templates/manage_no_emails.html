{% extends "base.html" %}

{% block title %}No Emails Review - Email Scraper{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-exclamation-triangle"></i> No Emails Review Dashboard
        </h1>
        <p class="text-muted">Manage companies where scraping was completed but no emails were found.</p>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Companies with No Emails</h6>
                        <h3 class="mb-0" id="noEmailsCount">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-exclamation-triangle" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">In Review</h6>
                        <h3 class="mb-0" id="inReviewCount">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-eye" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Reviewed</h6>
                        <h3 class="mb-0" id="reviewedCount">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-secondary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total Pages Scraped</h6>
                        <h3 class="mb-0" id="totalPagesScraped">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-file-earmark" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tab Navigation -->
<ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="no-emails-tab" data-bs-toggle="tab" data-bs-target="#no-emails" type="button" role="tab">
            <i class="bi bi-exclamation-triangle"></i> Companies with No Emails
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="review-tab" data-bs-toggle="tab" data-bs-target="#review" type="button" role="tab">
            <i class="bi bi-eye"></i> Review Dashboard
        </button>
    </li>
</ul>

<!-- Tab Content -->
<div class="tab-content" id="mainTabContent">
    <!-- No Emails Tab -->
    <div class="tab-pane fade show active" id="no-emails" role="tabpanel">
        <!-- Controls Section -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="noEmailsSearchInput" class="form-label">Search Companies</label>
                                <input type="text" class="form-control" id="noEmailsSearchInput" placeholder="Search by name or URL...">
                            </div>
                            <div class="col-md-3">
                                <label for="noEmailsSortSelect" class="form-label">Sort By</label>
                                <select class="form-select" id="noEmailsSortSelect">
                                    <option value="last_completed">Last Completed</option>
                                    <option value="name">Name (A-Z)</option>
                                    <option value="name_desc">Name (Z-A)</option>
                                    <option value="url">URL (A-Z)</option>
                                    <option value="url_desc">URL (Z-A)</option>
                                    <option value="pages_scraped">Pages Scraped</option>
                                    <option value="pages_scraped_desc">Pages Scraped (Desc)</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="button" class="btn btn-outline-secondary me-2" id="clearNoEmailsFiltersBtn">
                                    <i class="bi bi-x-circle"></i> Clear
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">Bulk Actions</h6>
                        <button type="button" class="btn btn-warning btn-sm me-2" id="bulkMigrateBtn" disabled>
                            <i class="bi bi-arrow-right"></i> Migrate Selected
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" id="refreshNoEmailsBtn">
                            <i class="bi bi-arrow-clockwise"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Companies Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list-ul"></i> Companies with No Emails Found
                        </h5>
                        <span class="badge bg-warning" id="noEmailsFilteredCount">0</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="noEmailsTable">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="selectAllNoEmails" class="form-check-input">
                                        </th>
                                        <th>Name</th>
                                        <th>URL</th>
                                        <th>Last Completed</th>
                                        <th>Jobs</th>
                                        <th>Pages Scraped</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="noEmailsTableBody">
                                    <!-- Companies will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                        <div id="noEmailsNoResults" class="text-center py-4" style="display: none;">
                            <i class="bi bi-search text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-2">No companies found matching your criteria.</p>
                        </div>
                        <div id="noEmailsLoadingSpinner" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Review Tab -->
    <div class="tab-pane fade" id="review" role="tabpanel">
        <!-- Review Controls Section -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="reviewSearchInput" class="form-label">Search Companies</label>
                                <input type="text" class="form-control" id="reviewSearchInput" placeholder="Search by name or URL...">
                            </div>
                            <div class="col-md-3">
                                <label for="reviewStatusFilter" class="form-label">Status Filter</label>
                                <select class="form-select" id="reviewStatusFilter">
                                    <option value="">All Statuses</option>
                                    <option value="pending">Pending</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="completed">Completed</option>
                                    <option value="rejected">Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="reviewSortSelect" class="form-label">Sort By</label>
                                <select class="form-select" id="reviewSortSelect">
                                    <option value="moved_at">Recently Moved</option>
                                    <option value="name">Name (A-Z)</option>
                                    <option value="name_desc">Name (Z-A)</option>
                                    <option value="review_status">Status</option>
                                    <option value="reviewed_at">Recently Reviewed</option>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="button" class="btn btn-outline-secondary" id="clearReviewFiltersBtn">
                                    <i class="bi bi-x-circle"></i> Clear
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">Actions</h6>
                        <button type="button" class="btn btn-outline-primary btn-sm" id="refreshReviewBtn">
                            <i class="bi bi-arrow-clockwise"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Review Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list-ul"></i> Companies Under Review
                        </h5>
                        <span class="badge bg-info" id="reviewFilteredCount">0</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="reviewTable">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>URL</th>
                                        <th>Moved Date</th>
                                        <th>Status</th>
                                        <th>Reviewed By</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="reviewTableBody">
                                    <!-- Review companies will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                        <div id="reviewNoResults" class="text-center py-4" style="display: none;">
                            <i class="bi bi-search text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-2">No companies found matching your criteria.</p>
                        </div>
                        <div id="reviewLoadingSpinner" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Migrate Confirmation Modal -->
<div class="modal fade" id="migrateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Migration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to migrate the selected companies to the review table?</p>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>Note:</strong> This will move the companies to the review dashboard for further analysis.
                </div>
                <div id="migrateCompanyInfo">
                    <!-- Company info will be populated by JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmMigrateBtn">Migrate Companies</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Review Company Modal -->
<div class="modal fade" id="editReviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Review Company</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editReviewForm">
                <div class="modal-body">
                    <input type="hidden" id="editReviewId">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editReviewCompanyName" class="form-label">Company Name</label>
                                <input type="text" class="form-control" id="editReviewCompanyName" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editReviewCompanyUrl" class="form-label">Company URL</label>
                                <input type="url" class="form-control" id="editReviewCompanyUrl" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editReviewStatus" class="form-label">Review Status *</label>
                                <select class="form-select" id="editReviewStatus" required>
                                    <option value="pending">Pending</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="completed">Completed</option>
                                    <option value="rejected">Rejected</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editReviewedBy" class="form-label">Reviewed By</label>
                                <input type="text" class="form-control" id="editReviewedBy" placeholder="Enter reviewer name">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editActionTaken" class="form-label">Action Taken</label>
                        <select class="form-select" id="editActionTaken">
                            <option value="">Select action...</option>
                            <option value="manual_search">Manual Email Search</option>
                            <option value="contact_form">Contact Form Available</option>
                            <option value="social_media">Social Media Contact</option>
                            <option value="phone_contact">Phone Contact Available</option>
                            <option value="no_contact">No Contact Method Found</option>
                            <option value="website_down">Website Down/Inactive</option>
                            <option value="redirect">Website Redirects</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editReviewNotes" class="form-label">Review Notes</label>
                        <textarea class="form-control" id="editReviewNotes" rows="4" placeholder="Enter detailed notes about the review..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Review</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/manage_no_emails.js') }}"></script>
{% endblock %}
